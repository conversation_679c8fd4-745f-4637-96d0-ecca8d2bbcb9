"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/sections/Features.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Features.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Features; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-intersection-observer */ \"../../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckBadgeIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckBadgeIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Features() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_3__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    // Icon mapping\n    const iconMap = {\n        users: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UserGroupIcon,\n        cpu: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CpuChipIcon,\n        shield: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShieldCheckIcon,\n        support: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChatBubbleLeftRightIcon,\n        currency: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CurrencyDollarIcon,\n        badge: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CheckBadgeIcon\n    };\n    // Get features from translation\n    const features = Array.from({\n        length: 6\n    }, (_, i)=>({\n            title: t(\"features.items.\".concat(i, \".title\")),\n            description: t(\"features.items.\".concat(i, \".description\")),\n            icon: t(\"features.items.\".concat(i, \".icon\"))\n        }));\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        ref: ref,\n        className: \"section-padding bg-white dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto container-padding\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: inView ? \"visible\" : \"hidden\",\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                            variants: itemVariants,\n                            className: \"heading-lg mb-4 text-gray-900 dark:text-white\",\n                            children: t(\"features.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                            variants: itemVariants,\n                            className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                            children: t(\"features.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: inView ? \"visible\" : \"hidden\",\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: features.map((feature, index)=>{\n                        const IconComponent = iconMap[feature.icon];\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            className: \"card card-hover p-8 text-center group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"w-8 h-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"heading-sm mb-4 text-gray-900 dark:text-white\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    variants: itemVariants,\n                    initial: \"hidden\",\n                    animate: inView ? \"visible\" : \"hidden\",\n                    className: \"text-center mt-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8 lg:p-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"heading-md mb-4 text-gray-900 dark:text-white\",\n                                children: isRTL ? \"جاهز للبدء؟\" : \"Ready to Get Started?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto\",\n                                children: isRTL ? \"انضم إلى آلاف الخبراء والعملاء الذين يثقون بمنصتنا لتحقيق أهدافهم المهنية\" : \"Join thousands of experts and clients who trust our platform to achieve their professional goals\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row items-center justify-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn-primary text-lg px-8 py-4\",\n                                        children: isRTL ? \"ابدأ كخبير\" : \"Start as Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn-secondary text-lg px-8 py-4\",\n                                        children: isRTL ? \"ابحث عن خبير\" : \"Find an Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(Features, \"HqYWKtUK8eWK2y93iaorxYfPYDQ=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_3__.useInView\n    ];\n});\n_c = Features;\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/sections/Features.tsx\n"));

/***/ })

});