"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/sections/About.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/About.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ About; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-intersection-observer */ \"../../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_LightBulbIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,LightBulbIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=HeartIcon,LightBulbIcon,StarIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction About() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_3__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    // Icon mapping for values\n    const iconMap = {\n        trust: _barrel_optimize_names_HeartIcon_LightBulbIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HeartIcon,\n        quality: _barrel_optimize_names_HeartIcon_LightBulbIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.StarIcon,\n        innovation: _barrel_optimize_names_HeartIcon_LightBulbIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.LightBulbIcon,\n        community: _barrel_optimize_names_HeartIcon_LightBulbIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UserGroupIcon\n    };\n    // Get values from translation\n    const values = Array.from({\n        length: 4\n    }, (_, i)=>{\n        const title = t(\"about.values.\".concat(i, \".title\"));\n        const description = t(\"about.values.\".concat(i, \".description\"));\n        // Map Arabic titles to icon keys\n        const iconKey = title === \"الثقة\" ? \"trust\" : title === \"الجودة\" ? \"quality\" : title === \"الابتكار\" ? \"innovation\" : title === \"المجتمع\" ? \"community\" : title === \"Trust\" ? \"trust\" : title === \"Quality\" ? \"quality\" : title === \"Innovation\" ? \"innovation\" : title === \"Community\" ? \"community\" : \"trust\";\n        return {\n            title,\n            description,\n            icon: iconKey\n        };\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        ref: ref,\n        className: \"section-padding bg-white dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto container-padding\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"heading-lg mb-4 text-gray-900 dark:text-white\",\n                                children: t(\"about.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                variants: itemVariants,\n                                className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                                children: t(\"about.subtitle\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                variants: itemVariants,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: t(\"about.content\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary-50 dark:bg-primary-900/20 rounded-lg p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-900 dark:text-white mb-2\",\n                                                        children: isRTL ? \"مهمتنا\" : \"Our Mission\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-300\",\n                                                        children: t(\"about.mission\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-secondary-50 dark:bg-secondary-900/20 rounded-lg p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-900 dark:text-white mb-2\",\n                                                        children: isRTL ? \"رؤيتنا\" : \"Our Vision\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-300\",\n                                                        children: t(\"about.vision\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                variants: itemVariants,\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-2xl p-8 lg:p-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2 rtl:space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-16 bg-syrian-red rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-16 bg-syrian-white border border-gray-200 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-16 bg-syrian-black rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-16 bg-syrian-green rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"heading-sm mb-4 text-gray-900 dark:text-white\",\n                                                    children: isRTL ? \"فخورون بهويتنا السورية\" : \"Proud of Our Syrian Identity\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-300\",\n                                                    children: isRTL ? \"نعمل على تمكين المواهب السورية وربطها بالفرص العالمية\" : \"We work to empower Syrian talents and connect them with global opportunities\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                        children: values.map((value, index)=>{\n                            const IconComponent = iconMap[value.icon];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                variants: itemVariants,\n                                className: \"text-center group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"w-8 h-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"heading-sm mb-3 text-gray-900 dark:text-white\",\n                                        children: value.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\",\n                                        children: value.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: itemVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"text-center mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8 lg:p-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"heading-md mb-4 text-gray-900 dark:text-white\",\n                                    children: isRTL ? \"انضم إلى رحلتنا\" : \"Join Our Journey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto\",\n                                    children: isRTL ? \"كن جزءاً من قصة نجاح فريلا سوريا وساهم في بناء مستقبل أفضل للعمل الحر في سوريا\" : \"Be part of Freela Syria's success story and contribute to building a better future for freelancing in Syria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row items-center justify-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-primary text-lg px-8 py-4\",\n                                            children: isRTL ? \"ابدأ رحلتك\" : \"Start Your Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-secondary text-lg px-8 py-4\",\n                                            children: isRTL ? \"تعرف على الفريق\" : \"Meet the Team\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(About, \"HqYWKtUK8eWK2y93iaorxYfPYDQ=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_3__.useInView\n    ];\n});\n_c = About;\nvar _c;\n$RefreshReg$(_c, \"About\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/sections/About.tsx\n"));

/***/ })

});