"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/sections/Features.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Features.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Features; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-intersection-observer */ \"../../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckBadgeIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckBadgeIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Features() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    // Icon mapping\n    const iconMap = {\n        users: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserGroupIcon,\n        cpu: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CpuChipIcon,\n        shield: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ShieldCheckIcon,\n        support: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChatBubbleLeftRightIcon,\n        currency: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CurrencyDollarIcon,\n        badge: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CheckBadgeIcon\n    };\n    // Get features from translation\n    const features = Array.from({\n        length: 6\n    }, (_, i)=>({\n            title: t(\"features.items.\".concat(i, \".title\")),\n            description: t(\"features.items.\".concat(i, \".description\")),\n            icon: t(\"features.items.\".concat(i, \".icon\"))\n        }));\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        ref: ref,\n        className: \"relative section-padding overflow-hidden\",\n        style: {\n            background: \"\\n          radial-gradient(circle at \".concat(mousePosition.x, \"% \").concat(mousePosition.y, \"%, rgba(206, 17, 38, 0.08) 0%, transparent 60%),\\n          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\\n        \")\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                -20,\n                                20,\n                                -20\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 18,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-20 right-10 w-28 h-28 rounded-full opacity-15\",\n                        style: {\n                            background: \"rgba(206, 17, 38, 0.1)\",\n                            backdropFilter: \"blur(20px)\",\n                            WebkitBackdropFilter: \"blur(20px)\",\n                            border: \"1px solid rgba(206, 17, 38, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(206, 17, 38, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                30,\n                                -30,\n                                30\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ],\n                            scale: [\n                                1.1,\n                                1,\n                                1.1\n                            ]\n                        },\n                        transition: {\n                            duration: 22,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-32 left-16 w-36 h-36 rounded-full opacity-12\",\n                        style: {\n                            background: \"rgba(0, 122, 61, 0.1)\",\n                            backdropFilter: \"blur(25px)\",\n                            WebkitBackdropFilter: \"blur(25px)\",\n                            border: \"1px solid rgba(0, 122, 61, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(0, 122, 61, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    [\n                        ...Array(8)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            animate: {\n                                y: [\n                                    -15,\n                                    15,\n                                    -15\n                                ],\n                                x: [\n                                    -8,\n                                    8,\n                                    -8\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 6 + i * 1.5,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: i * 0.4\n                            },\n                            className: \"absolute w-1.5 h-1.5 bg-white rounded-full\",\n                            style: {\n                                left: \"\".concat(15 + i * 10, \"%\"),\n                                top: \"\".concat(25 + i * 8, \"%\"),\n                                filter: \"blur(0.5px)\"\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto container-padding relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"text-center mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                variants: itemVariants,\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h2, {\n                                        className: \"heading-lg mb-6 text-white relative z-10 px-8 py-4 text-arabic-premium\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30,\n                                            filter: \"blur(10px)\"\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0,\n                                            filter: \"blur(0px)\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\"\n                                        },\n                                        children: t(\"features.title\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 -m-4 rounded-2xl opacity-25\",\n                                        style: {\n                                            background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%)\",\n                                            backdropFilter: \"blur(30px)\",\n                                            WebkitBackdropFilter: \"blur(30px)\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.12)\",\n                                            boxShadow: \"0 20px 40px rgba(0, 0, 0, 0.08)\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                variants: itemVariants,\n                                className: \"text-xl text-white/90 max-w-3xl mx-auto leading-relaxed text-arabic px-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3\n                                },\n                                children: t(\"features.subtitle\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\",\n                        children: features.map((feature, index)=>{\n                            const IconComponent = iconMap[feature.icon];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30,\n                                    scale: 0.9\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.6 + index * 0.1,\n                                    type: \"spring\",\n                                    stiffness: 100\n                                },\n                                whileHover: {\n                                    scale: 1.05,\n                                    y: -8,\n                                    transition: {\n                                        duration: 0.2\n                                    }\n                                },\n                                className: \"card-premium text-center p-8 group cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"flex items-center justify-center mb-6\",\n                                        whileHover: {\n                                            scale: 1.1,\n                                            rotate: 5\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 rounded-3xl flex items-center justify-center relative overflow-hidden\",\n                                            style: {\n                                                background: index % 3 === 0 ? \"linear-gradient(135deg, rgba(206, 17, 38, 0.2) 0%, rgba(206, 17, 38, 0.1) 100%)\" : index % 3 === 1 ? \"linear-gradient(135deg, rgba(0, 122, 61, 0.2) 0%, rgba(0, 122, 61, 0.1) 100%)\" : \"linear-gradient(135deg, rgba(2, 132, 199, 0.2) 0%, rgba(2, 132, 199, 0.1) 100%)\",\n                                                backdropFilter: \"blur(15px)\",\n                                                WebkitBackdropFilter: \"blur(15px)\",\n                                                border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: \"w-10 h-10 text-white group-hover:scale-110 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                                    style: {\n                                                        background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                        backgroundSize: \"200% 100%\",\n                                                        animation: \"glassShimmer 2s ease-in-out infinite\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h3, {\n                                        className: \"heading-sm mb-4 text-white group-hover:scale-105 transition-transform duration-300 text-arabic-premium\",\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.8 + index * 0.1\n                                        },\n                                        children: feature.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                        className: \"text-white/80 leading-relaxed text-arabic\",\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.9 + index * 0.1\n                                        },\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: itemVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"text-center mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8 lg:p-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"heading-md mb-4 text-gray-900 dark:text-white\",\n                                    children: isRTL ? \"جاهز للبدء؟\" : \"Ready to Get Started?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto\",\n                                    children: isRTL ? \"انضم إلى آلاف الخبراء والعملاء الذين يثقون بمنصتنا لتحقيق أهدافهم المهنية\" : \"Join thousands of experts and clients who trust our platform to achieve their professional goals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row items-center justify-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-primary text-lg px-8 py-4\",\n                                            children: isRTL ? \"ابدأ كخبير\" : \"Start as Expert\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-secondary text-lg px-8 py-4\",\n                                            children: isRTL ? \"ابحث عن خبير\" : \"Find an Expert\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(Features, \"3vKVmfN1OZFyqiF+gCOAKi3qgaQ=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__.useInView\n    ];\n});\n_c = Features;\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/sections/Features.tsx\n"));

/***/ })

});