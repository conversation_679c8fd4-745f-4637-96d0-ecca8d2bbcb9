"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/sections/About.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/About.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ About; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-intersection-observer */ \"../../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_HeartIcon_LightBulbIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,LightBulbIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=HeartIcon,LightBulbIcon,StarIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction About() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    // Icon mapping for values\n    const iconMap = {\n        trust: _barrel_optimize_names_HeartIcon_LightBulbIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.HeartIcon,\n        quality: _barrel_optimize_names_HeartIcon_LightBulbIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.StarIcon,\n        innovation: _barrel_optimize_names_HeartIcon_LightBulbIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.LightBulbIcon,\n        community: _barrel_optimize_names_HeartIcon_LightBulbIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserGroupIcon\n    };\n    // Get values from translation\n    const values = Array.from({\n        length: 4\n    }, (_, i)=>{\n        const title = t(\"about.values.\".concat(i, \".title\"));\n        const description = t(\"about.values.\".concat(i, \".description\"));\n        // Map Arabic titles to icon keys\n        const iconKey = title === \"الثقة\" ? \"trust\" : title === \"الجودة\" ? \"quality\" : title === \"الابتكار\" ? \"innovation\" : title === \"المجتمع\" ? \"community\" : title === \"Trust\" ? \"trust\" : title === \"Quality\" ? \"quality\" : title === \"Innovation\" ? \"innovation\" : title === \"Community\" ? \"community\" : \"trust\";\n        return {\n            title,\n            description,\n            icon: iconKey\n        };\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        ref: ref,\n        className: \"relative section-padding overflow-hidden\",\n        style: {\n            background: \"\\n          radial-gradient(circle at \".concat(mousePosition.x, \"% \").concat(mousePosition.y, \"%, rgba(0, 122, 61, 0.08) 0%, transparent 60%),\\n          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\\n        \")\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                -25,\n                                25,\n                                -25\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-32 left-12 w-32 h-32 rounded-full opacity-12\",\n                        style: {\n                            background: \"rgba(0, 122, 61, 0.1)\",\n                            backdropFilter: \"blur(25px)\",\n                            WebkitBackdropFilter: \"blur(25px)\",\n                            border: \"1px solid rgba(0, 122, 61, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(0, 122, 61, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                35,\n                                -35,\n                                35\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ],\n                            scale: [\n                                1.1,\n                                1,\n                                1.1\n                            ]\n                        },\n                        transition: {\n                            duration: 24,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-40 right-20 w-28 h-28 rounded-full opacity-15\",\n                        style: {\n                            background: \"rgba(206, 17, 38, 0.1)\",\n                            backdropFilter: \"blur(20px)\",\n                            WebkitBackdropFilter: \"blur(20px)\",\n                            border: \"1px solid rgba(206, 17, 38, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(206, 17, 38, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    [\n                        ...Array(10)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            animate: {\n                                y: [\n                                    -18,\n                                    18,\n                                    -18\n                                ],\n                                x: [\n                                    -9,\n                                    9,\n                                    -9\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 7 + i * 1.8,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: i * 0.6\n                            },\n                            className: \"absolute w-1.5 h-1.5 bg-white rounded-full\",\n                            style: {\n                                left: \"\".concat(12 + i * 9, \"%\"),\n                                top: \"\".concat(20 + i * 7, \"%\"),\n                                filter: \"blur(0.5px)\"\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto container-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: inView ? \"visible\" : \"hidden\",\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h2, {\n                                    variants: itemVariants,\n                                    className: \"heading-lg mb-4 text-gray-900 dark:text-white\",\n                                    children: t(\"about.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                    variants: itemVariants,\n                                    className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                                    children: t(\"about.subtitle\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                            children: t(\"about.content\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary-50 dark:bg-primary-900/20 rounded-lg p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 dark:text-white mb-2\",\n                                                            children: isRTL ? \"مهمتنا\" : \"Our Mission\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-300\",\n                                                            children: t(\"about.mission\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-secondary-50 dark:bg-secondary-900/20 rounded-lg p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 dark:text-white mb-2\",\n                                                            children: isRTL ? \"رؤيتنا\" : \"Our Vision\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-300\",\n                                                            children: t(\"about.vision\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-2xl p-8 lg:p-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 rtl:space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-16 bg-syrian-red rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-16 bg-syrian-white border border-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-16 bg-syrian-black rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-16 bg-syrian-green rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"heading-sm mb-4 text-gray-900 dark:text-white\",\n                                                        children: isRTL ? \"فخورون بهويتنا السورية\" : \"Proud of Our Syrian Identity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-300\",\n                                                        children: isRTL ? \"نعمل على تمكين المواهب السورية وربطها بالفرص العالمية\" : \"We work to empower Syrian talents and connect them with global opportunities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            variants: containerVariants,\n                            initial: \"hidden\",\n                            animate: inView ? \"visible\" : \"hidden\",\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: values.map((value, index)=>{\n                                const IconComponent = iconMap[value.icon];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"text-center group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"heading-sm mb-3 text-gray-900 dark:text-white\",\n                                            children: value.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\",\n                                            children: value.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            variants: itemVariants,\n                            initial: \"hidden\",\n                            animate: inView ? \"visible\" : \"hidden\",\n                            className: \"text-center mt-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8 lg:p-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"heading-md mb-4 text-gray-900 dark:text-white\",\n                                        children: isRTL ? \"انضم إلى رحلتنا\" : \"Join Our Journey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto\",\n                                        children: isRTL ? \"كن جزءاً من قصة نجاح فريلا سوريا وساهم في بناء مستقبل أفضل للعمل الحر في سوريا\" : \"Be part of Freela Syria's success story and contribute to building a better future for freelancing in Syria\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row items-center justify-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-primary text-lg px-8 py-4\",\n                                                children: isRTL ? \"ابدأ رحلتك\" : \"Start Your Journey\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-secondary text-lg px-8 py-4\",\n                                                children: isRTL ? \"تعرف على الفريق\" : \"Meet the Team\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\About.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(About, \"3vKVmfN1OZFyqiF+gCOAKi3qgaQ=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__.useInView\n    ];\n});\n_c = About;\nvar _c;\n$RefreshReg$(_c, \"About\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/sections/About.tsx\n"));

/***/ })

});