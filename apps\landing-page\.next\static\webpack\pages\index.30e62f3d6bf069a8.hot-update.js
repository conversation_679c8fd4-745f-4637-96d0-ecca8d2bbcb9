"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckBadgeIcon,CpuChipIcon,CurrencyDollarIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckBadgeIcon,CpuChipIcon,CurrencyDollarIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatBubbleLeftRightIcon: function() { return /* reexport safe */ _ChatBubbleLeftRightIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   CheckBadgeIcon: function() { return /* reexport safe */ _CheckBadgeIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   CpuChipIcon: function() { return /* reexport safe */ _CpuChipIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   CurrencyDollarIcon: function() { return /* reexport safe */ _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   RocketLaunchIcon: function() { return /* reexport safe */ _RocketLaunchIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   ShieldCheckIcon: function() { return /* reexport safe */ _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; },\n/* harmony export */   SparklesIcon: function() { return /* reexport safe */ _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]; },\n/* harmony export */   UserGroupIcon: function() { return /* reexport safe */ _UserGroupIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _ChatBubbleLeftRightIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChatBubbleLeftRightIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _CheckBadgeIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CheckBadgeIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/CheckBadgeIcon.js\");\n/* harmony import */ var _CpuChipIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CpuChipIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CurrencyDollarIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _RocketLaunchIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RocketLaunchIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ShieldCheckIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./SparklesIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _UserGroupIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./UserGroupIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGF0QnViYmxlTGVmdFJpZ2h0SWNvbixDaGVja0JhZGdlSWNvbixDcHVDaGlwSWNvbixDdXJyZW5jeURvbGxhckljb24sUm9ja2V0TGF1bmNoSWNvbixTaGllbGRDaGVja0ljb24sU3BhcmtsZXNJY29uLFVzZXJHcm91cEljb24hPSEuLi8uLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNpRjtBQUNsQjtBQUNOO0FBQ2M7QUFDSjtBQUNGO0FBQ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzPzBmNTciXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXRCdWJibGVMZWZ0UmlnaHRJY29uIH0gZnJvbSBcIi4vQ2hhdEJ1YmJsZUxlZnRSaWdodEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVja0JhZGdlSWNvbiB9IGZyb20gXCIuL0NoZWNrQmFkZ2VJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ3B1Q2hpcEljb24gfSBmcm9tIFwiLi9DcHVDaGlwSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEN1cnJlbmN5RG9sbGFySWNvbiB9IGZyb20gXCIuL0N1cnJlbmN5RG9sbGFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFJvY2tldExhdW5jaEljb24gfSBmcm9tIFwiLi9Sb2NrZXRMYXVuY2hJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hpZWxkQ2hlY2tJY29uIH0gZnJvbSBcIi4vU2hpZWxkQ2hlY2tJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3BhcmtsZXNJY29uIH0gZnJvbSBcIi4vU3BhcmtsZXNJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckdyb3VwSWNvbiB9IGZyb20gXCIuL1VzZXJHcm91cEljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckBadgeIcon,CpuChipIcon,CurrencyDollarIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./src/components/sections/Features.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Features.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Features; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-intersection-observer */ \"../../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckBadgeIcon,CpuChipIcon,CurrencyDollarIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckBadgeIcon,CpuChipIcon,CurrencyDollarIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Features() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    // Icon mapping\n    const iconMap = {\n        users: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserGroupIcon,\n        cpu: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CpuChipIcon,\n        shield: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ShieldCheckIcon,\n        support: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChatBubbleLeftRightIcon,\n        currency: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CurrencyDollarIcon,\n        badge: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CheckBadgeIcon\n    };\n    // Get features from translation\n    const features = Array.from({\n        length: 6\n    }, (_, i)=>({\n            title: t(\"features.items.\".concat(i, \".title\")),\n            description: t(\"features.items.\".concat(i, \".description\")),\n            icon: t(\"features.items.\".concat(i, \".icon\"))\n        }));\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        ref: ref,\n        className: \"relative section-padding overflow-hidden\",\n        style: {\n            background: \"\\n          radial-gradient(circle at \".concat(mousePosition.x, \"% \").concat(mousePosition.y, \"%, rgba(206, 17, 38, 0.08) 0%, transparent 60%),\\n          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\\n        \")\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                -20,\n                                20,\n                                -20\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 18,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-20 right-10 w-28 h-28 rounded-full opacity-15\",\n                        style: {\n                            background: \"rgba(206, 17, 38, 0.1)\",\n                            backdropFilter: \"blur(20px)\",\n                            WebkitBackdropFilter: \"blur(20px)\",\n                            border: \"1px solid rgba(206, 17, 38, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(206, 17, 38, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                30,\n                                -30,\n                                30\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ],\n                            scale: [\n                                1.1,\n                                1,\n                                1.1\n                            ]\n                        },\n                        transition: {\n                            duration: 22,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-32 left-16 w-36 h-36 rounded-full opacity-12\",\n                        style: {\n                            background: \"rgba(0, 122, 61, 0.1)\",\n                            backdropFilter: \"blur(25px)\",\n                            WebkitBackdropFilter: \"blur(25px)\",\n                            border: \"1px solid rgba(0, 122, 61, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(0, 122, 61, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    [\n                        ...Array(8)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            animate: {\n                                y: [\n                                    -15,\n                                    15,\n                                    -15\n                                ],\n                                x: [\n                                    -8,\n                                    8,\n                                    -8\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 6 + i * 1.5,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: i * 0.4\n                            },\n                            className: \"absolute w-1.5 h-1.5 bg-white rounded-full\",\n                            style: {\n                                left: \"\".concat(15 + i * 10, \"%\"),\n                                top: \"\".concat(25 + i * 8, \"%\"),\n                                filter: \"blur(0.5px)\"\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto container-padding relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"text-center mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                variants: itemVariants,\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h2, {\n                                        className: \"heading-lg mb-6 text-white relative z-10 px-8 py-4 text-arabic-premium\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30,\n                                            filter: \"blur(10px)\"\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0,\n                                            filter: \"blur(0px)\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\"\n                                        },\n                                        children: t(\"features.title\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 -m-4 rounded-2xl opacity-25\",\n                                        style: {\n                                            background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%)\",\n                                            backdropFilter: \"blur(30px)\",\n                                            WebkitBackdropFilter: \"blur(30px)\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.12)\",\n                                            boxShadow: \"0 20px 40px rgba(0, 0, 0, 0.08)\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                variants: itemVariants,\n                                className: \"text-xl text-white/90 max-w-3xl mx-auto leading-relaxed text-arabic px-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3\n                                },\n                                children: t(\"features.subtitle\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\",\n                        children: features.map((feature, index)=>{\n                            const IconComponent = iconMap[feature.icon];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30,\n                                    scale: 0.9\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.6 + index * 0.1,\n                                    type: \"spring\",\n                                    stiffness: 100\n                                },\n                                whileHover: {\n                                    scale: 1.05,\n                                    y: -8,\n                                    transition: {\n                                        duration: 0.2\n                                    }\n                                },\n                                className: \"card-premium text-center p-8 group cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"flex items-center justify-center mb-6\",\n                                        whileHover: {\n                                            scale: 1.1,\n                                            rotate: 5\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 rounded-3xl flex items-center justify-center relative overflow-hidden\",\n                                            style: {\n                                                background: index % 3 === 0 ? \"linear-gradient(135deg, rgba(206, 17, 38, 0.2) 0%, rgba(206, 17, 38, 0.1) 100%)\" : index % 3 === 1 ? \"linear-gradient(135deg, rgba(0, 122, 61, 0.2) 0%, rgba(0, 122, 61, 0.1) 100%)\" : \"linear-gradient(135deg, rgba(2, 132, 199, 0.2) 0%, rgba(2, 132, 199, 0.1) 100%)\",\n                                                backdropFilter: \"blur(15px)\",\n                                                WebkitBackdropFilter: \"blur(15px)\",\n                                                border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: \"w-10 h-10 text-white group-hover:scale-110 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                                    style: {\n                                                        background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                        backgroundSize: \"200% 100%\",\n                                                        animation: \"glassShimmer 2s ease-in-out infinite\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h3, {\n                                        className: \"heading-sm mb-4 text-white group-hover:scale-105 transition-transform duration-300 text-arabic-premium\",\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.8 + index * 0.1\n                                        },\n                                        children: feature.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                        className: \"text-white/80 leading-relaxed text-arabic\",\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.9 + index * 0.1\n                                        },\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: itemVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-premium p-12 lg:p-16 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-4 right-4 flex space-x-1 rtl:space-x-reverse opacity-30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-8 bg-syrian-red rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-8 bg-syrian-green rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h3, {\n                                    className: \"heading-md mb-6 text-white text-arabic-premium\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.2\n                                    },\n                                    children: isRTL ? \"جاهز للبدء؟\" : \"Ready to Get Started?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                    className: \"text-xl text-white/90 mb-10 max-w-2xl mx-auto leading-relaxed text-arabic\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.4\n                                    },\n                                    children: isRTL ? \"انضم إلى آلاف الخبراء والعملاء الذين يثقون بمنصتنا لتحقيق أهدافهم المهنية\" : \"Join thousands of experts and clients who trust our platform to achieve their professional goals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    className: \"flex flex-col sm:flex-row items-center justify-center gap-6\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.6\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                            type: \"button\",\n                                            whileHover: {\n                                                scale: 1.05,\n                                                y: -2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                type: \"spring\",\n                                                stiffness: 400,\n                                                damping: 17\n                                            },\n                                            className: \"glass-button text-white text-lg font-bold px-10 py-4 w-full sm:w-auto inline-flex items-center justify-center gap-3 group text-arabic-premium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.RocketLaunchIcon, {\n                                                    className: \"w-6 h-6 group-hover:rotate-12 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isRTL ? \"ابدأ كخبير\" : \"Start as Expert\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.SparklesIcon, {\n                                                    className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                            type: \"button\",\n                                            whileHover: {\n                                                scale: 1.02,\n                                                y: -1\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                type: \"spring\",\n                                                stiffness: 400,\n                                                damping: 17\n                                            },\n                                            className: \"glass-button text-white/95 text-lg font-semibold px-8 py-4 w-full sm:w-auto flex items-center justify-center gap-3 group text-arabic\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_CheckBadgeIcon_CpuChipIcon_CurrencyDollarIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserGroupIcon, {\n                                                    className: \"w-6 h-6 group-hover:scale-110 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isRTL ? \"ابحث عن خبير\" : \"Find an Expert\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Features.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(Features, \"3vKVmfN1OZFyqiF+gCOAKi3qgaQ=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__.useInView\n    ];\n});\n_c = Features;\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/sections/Features.tsx\n"));

/***/ }),

/***/ "../../node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n\nfunction RocketLaunchIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(RocketLaunchIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\n"));

/***/ }),

/***/ "../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n\nfunction SparklesIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(SparklesIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\n"));

/***/ })

});