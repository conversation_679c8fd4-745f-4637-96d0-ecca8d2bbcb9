"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/sections/HowItWorks.tsx":
/*!************************************************!*\
  !*** ./src/components/sections/HowItWorks.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HowItWorks; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-intersection-observer */ \"../../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BanknotesIcon,DocumentPlusIcon,InboxArrowDownIcon,MagnifyingGlassIcon,PaperAirplaneIcon,PlayIcon,UserIcon,UserPlusIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BanknotesIcon,DocumentPlusIcon,InboxArrowDownIcon,MagnifyingGlassIcon,PaperAirplaneIcon,PlayIcon,UserIcon,UserPlusIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction HowItWorks() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"clients\");\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    // Steps for clients\n    const clientSteps = [\n        {\n            title: t(\"howItWorks.forClients.steps.0.title\"),\n            description: t(\"howItWorks.forClients.steps.0.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.DocumentPlusIcon\n        },\n        {\n            title: t(\"howItWorks.forClients.steps.1.title\"),\n            description: t(\"howItWorks.forClients.steps.1.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.InboxArrowDownIcon\n        },\n        {\n            title: t(\"howItWorks.forClients.steps.2.title\"),\n            description: t(\"howItWorks.forClients.steps.2.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserIcon\n        },\n        {\n            title: t(\"howItWorks.forClients.steps.3.title\"),\n            description: t(\"howItWorks.forClients.steps.3.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PlayIcon\n        }\n    ];\n    // Steps for experts\n    const expertSteps = [\n        {\n            title: t(\"howItWorks.forExperts.steps.0.title\"),\n            description: t(\"howItWorks.forExperts.steps.0.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserPlusIcon\n        },\n        {\n            title: t(\"howItWorks.forExperts.steps.1.title\"),\n            description: t(\"howItWorks.forExperts.steps.1.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.MagnifyingGlassIcon\n        },\n        {\n            title: t(\"howItWorks.forExperts.steps.2.title\"),\n            description: t(\"howItWorks.forExperts.steps.2.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PaperAirplaneIcon\n        },\n        {\n            title: t(\"howItWorks.forExperts.steps.3.title\"),\n            description: t(\"howItWorks.forExperts.steps.3.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.BanknotesIcon\n        }\n    ];\n    const currentSteps = activeTab === \"clients\" ? clientSteps : expertSteps;\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"how-it-works\",\n        ref: ref,\n        className: \"relative section-padding overflow-hidden\",\n        style: {\n            background: \"\\n          radial-gradient(circle at \".concat(mousePosition.x, \"% \").concat(mousePosition.y, \"%, rgba(2, 132, 199, 0.08) 0%, transparent 60%),\\n          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\\n        \")\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                -22,\n                                22,\n                                -22\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 19,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-24 right-16 w-30 h-30 rounded-full opacity-12\",\n                        style: {\n                            background: \"rgba(2, 132, 199, 0.1)\",\n                            backdropFilter: \"blur(22px)\",\n                            WebkitBackdropFilter: \"blur(22px)\",\n                            border: \"1px solid rgba(2, 132, 199, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(2, 132, 199, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                28,\n                                -28,\n                                28\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ],\n                            scale: [\n                                1.1,\n                                1,\n                                1.1\n                            ]\n                        },\n                        transition: {\n                            duration: 21,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-36 left-14 w-26 h-26 rounded-full opacity-10\",\n                        style: {\n                            background: \"rgba(124, 58, 237, 0.1)\",\n                            backdropFilter: \"blur(18px)\",\n                            WebkitBackdropFilter: \"blur(18px)\",\n                            border: \"1px solid rgba(124, 58, 237, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(124, 58, 237, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            animate: {\n                                y: [\n                                    -12,\n                                    12,\n                                    -12\n                                ],\n                                x: [\n                                    -6,\n                                    6,\n                                    -6\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 5 + i * 1.2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: i * 0.8\n                            },\n                            className: \"absolute w-1 h-1 bg-white rounded-full\",\n                            style: {\n                                left: \"\".concat(20 + i * 12, \"%\"),\n                                top: \"\".concat(30 + i * 10, \"%\"),\n                                filter: \"blur(0.5px)\"\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto container-padding\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"heading-lg mb-4 text-gray-900 dark:text-white\",\n                                children: t(\"howItWorks.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                variants: itemVariants,\n                                className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                                children: t(\"howItWorks.subtitle\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: itemVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"flex justify-center mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-700 rounded-xl p-2 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"clients\"),\n                                    className: \"px-6 py-3 rounded-lg font-medium transition-all duration-200 \".concat(activeTab === \"clients\" ? \"bg-primary-600 text-white shadow-md\" : \"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400\"),\n                                    children: t(\"howItWorks.forClients.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"experts\"),\n                                    className: \"px-6 py-3 rounded-lg font-medium transition-all duration-200 \".concat(activeTab === \"experts\" ? \"bg-primary-600 text-white shadow-md\" : \"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400\"),\n                                    children: t(\"howItWorks.forExperts.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: \"visible\",\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                        children: currentSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                variants: itemVariants,\n                                className: \"relative\",\n                                children: [\n                                    index < currentSteps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-primary-300 to-secondary-300 dark:from-primary-600 dark:to-secondary-600 z-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card p-6 text-center relative z-10 h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center text-white font-bold text-xl\",\n                                                children: index + 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 mx-auto mb-4 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                    className: \"w-6 h-6 text-primary-600 dark:text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"heading-sm mb-3 text-gray-900 dark:text-white\",\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\",\n                                                children: step.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this))\n                    }, activeTab, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: itemVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"text-center mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"btn-primary text-lg px-8 py-4\",\n                            children: activeTab === \"clients\" ? isRTL ? \"انشر مشروعك الآن\" : \"Post Your Project Now\" : isRTL ? \"انضم كخبير\" : \"Join as Expert\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(HowItWorks, \"TDOWiIeeiyRYv+PwmIzLMv4EnIg=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__.useInView\n    ];\n});\n_c = HowItWorks;\nvar _c;\n$RefreshReg$(_c, \"HowItWorks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/sections/HowItWorks.tsx\n"));

/***/ })

});